""""""  		  	   		 	 	 			  		 			 	 	 		 		 	
"""  		  	   		 	 	 			  		 			 	 	 		 		 	
Template for implementing QLearner  (c) 2015 Tucker <PERSON>ch  		  	   		 	 	 			  		 			 	 	 		 		 	
  		  	   		 	 	 			  		 			 	 	 		 		 	
Copyright 2018, Georgia Institute of Technology (Georgia Tech)  		  	   		 	 	 			  		 			 	 	 		 		 	
Atlanta, Georgia 30332  		  	   		 	 	 			  		 			 	 	 		 		 	
All Rights Reserved  		  	   		 	 	 			  		 			 	 	 		 		 	
  		  	   		 	 	 			  		 			 	 	 		 		 	
Template code for CS 4646/7646  		  	   		 	 	 			  		 			 	 	 		 		 	
  		  	   		 	 	 			  		 			 	 	 		 		 	
Georgia Tech asserts copyright ownership of this template and all derivative  		  	   		 	 	 			  		 			 	 	 		 		 	
works, including solutions to the projects assigned in this course. Students  		  	   		 	 	 			  		 			 	 	 		 		 	
and other users of this template code are advised not to share it with others  		  	   		 	 	 			  		 			 	 	 		 		 	
or to make it available on publicly viewable websites including repositories  		  	   		 	 	 			  		 			 	 	 		 		 	
such as github and gitlab.  This copyright statement should not be removed  		  	   		 	 	 			  		 			 	 	 		 		 	
or edited.  		  	   		 	 	 			  		 			 	 	 		 		 	
  		  	   		 	 	 			  		 			 	 	 		 		 	
We do grant permission to share solutions privately with non-students such  		  	   		 	 	 			  		 			 	 	 		 		 	
as potential employers. However, sharing with other current or future  		  	   		 	 	 			  		 			 	 	 		 		 	
students of CS 7646 is prohibited and subject to being investigated as a  		  	   		 	 	 			  		 			 	 	 		 		 	
GT honor code violation.  		  	   		 	 	 			  		 			 	 	 		 		 	
  		  	   		 	 	 			  		 			 	 	 		 		 	
-----do not edit anything above this line---  		  	   		 	 	 			  		 			 	 	 		 		 	
  		  	   		 	 	 			  		 			 	 	 		 		 	
Student Name: <PERSON>  		  	   		 	 	 			  		 			 	 	 		 		 	
GT User ID: hqian36  		  	   		 	 	 			  		 			 	 	 		 		 	
GT ID: 903538227  		  	   		 	 	 			  		 			 	 	 		 		 	
"""  		  	   		 	 	 			  		 			 	 	 		 		 	
  		  	   		 	 	 			  		 			 	 	 		 		 	
import random as rand  		  	   		 	 	 			  		 			 	 	 		 		 	
  		  	   		 	 	 			  		 			 	 	 		 		 	
import numpy as np  		  	   		 	 	 			  		 			 	 	 		 		 	
  		  	   		 	 	 			  		 			 	 	 		 		 	
import logging  		  	
logger = logging.getLogger(__name__)   		 	 	 			  		 			 	 	 		 		 	
class QLearner(object):  		  	   		 	 	 			  		 			 	 	 		 		 	
    """  		  	   		 	 	 			  		 			 	 	 		 		 	
    This is a Q learner object.  		  	   		 	 	 			  		 			 	 	 		 		 	
  		  	   		 	 	 			  		 			 	 	 		 		 	
    :param num_states: The number of states to consider.  		  	   		 	 	 			  		 			 	 	 		 		 	
    :type num_states: int  		  	   		 	 	 			  		 			 	 	 		 		 	
    :param num_actions: The number of actions available..  		  	   		 	 	 			  		 			 	 	 		 		 	
    :type num_actions: int  		  	   		 	 	 			  		 			 	 	 		 		 	
    :param alpha: The learning rate used in the update rule. Should range between 0.0 and 1.0 with 0.2 as a typical value.  		  	   		 	 	 			  		 			 	 	 		 		 	
    :type alpha: float  		  	   		 	 	 			  		 			 	 	 		 		 	
    :param gamma: The discount rate used in the update rule. Should range between 0.0 and 1.0 with 0.9 as a typical value.  		  	   		 	 	 			  		 			 	 	 		 		 	
    :type gamma: float  		  	   		 	 	 			  		 			 	 	 		 		 	
    :param rar: Random action rate: the probability of selecting a random action at each step. Should range between 0.0 (no random actions) to 1.0 (always random action) with 0.5 as a typical value.  		  	   		 	 	 			  		 			 	 	 		 		 	
    :type rar: float  		  	   		 	 	 			  		 			 	 	 		 		 	
    :param radr: Random action decay rate, after each update, rar = rar * radr. Ranges between 0.0 (immediate decay to 0) and 1.0 (no decay). Typically 0.99.  		  	   		 	 	 			  		 			 	 	 		 		 	
    :type radr: float  		  	   		 	 	 			  		 			 	 	 		 		 	
    :param dyna: The number of dyna updates for each regular update. When Dyna is used, 200 is a typical value.  		  	   		 	 	 			  		 			 	 	 		 		 	
    :type dyna: int  		  	   		 	 	 			  		 			 	 	 		 		 	
    :param verbose: If “verbose” is True, your code can print out information for debugging.  		  	   		 	 	 			  		 			 	 	 		 		 	
    :type verbose: bool  		  	   		 	 	 			  		 			 	 	 		 		 	
    """  		  	   		 	 	 			  		 			 	 	 		 		 	
    def __init__(  		  	   		 	 	 			  		 			 	 	 		 		 	
        self,  		  	   		 	 	 			  		 			 	 	 		 		 	
        num_states=100,  		  	   		 	 	 			  		 			 	 	 		 		 	
        num_actions=4,  		  	   		 	 	 			  		 			 	 	 		 		 	
        alpha=0.2,  		  	   		 	 	 			  		 			 	 	 		 		 	
        gamma=0.9,  		  	   		 	 	 			  		 			 	 	 		 		 	
        rar=0.5,  		  	   		 	 	 			  		 			 	 	 		 		 	
        radr=0.99,  		  	   		 	 	 			  		 			 	 	 		 		 	
        dyna=0,  		  	   		 	 	 			  		 			 	 	 		 		 	
        verbose=False,  		  	   		 	 	 			  		 			 	 	 		 		 	
    ):  		  	   		 	 	 			  		 			 	 	 		 		 	
        """  		  	   		 	 	 			  		 			 	 	 		 		 	
        Constructor method  		  	   		 	 	 			  		 			 	 	 		 		 	
        """  		  	   		 	 	 			  		 			 	 	 		 		 	
        self.verbose = verbose  		  	   		 	 	 			  		 			 	 	 		 		 	
        self.num_actions = num_actions  		  	   		 	 	 			  		 			 	 	 		 		 	
        self.s = 0  		  	   		 	 	 			  		 			 	 	 		 		 	
        self.a = 0  		  	   		 	 	 			  		 			 	 	 		 		 	
        self.alpha = alpha
        self.gamma = gamma
        self.q = np.zeros((num_states, num_actions))
        self.rar = rar
        self.radr = radr
        self.dyna = dyna
        if self.verbose:
            logger.setLevel(logging.DEBUG)
        else:
            logger.setLevel(logging.INFO)
  	
    def _get_next(self, s_prime):
        if np.random.rand() < self.rar:
            action = np.random.randint(0, self.num_actions)
        else:
            action = np.argmax(self.q[s_prime, :])
        return action
    	  	   		 	 	 			  		 			 	 	 		 		 	
    def querysetstate(self, s):  		  	   		 	 	 			  		 			 	 	 		 		 	
        """  		  	   		 	 	 			  		 			 	 	 		 		 	
        Update the state without updating the Q-table  		  	   		 	 	 			  		 			 	 	 		 		 	
  		  	   		 	 	 			  		 			 	 	 		 		 	
        :param s: The new state  		  	   		 	 	 			  		 			 	 	 		 		 	
        :type s: int  		  	   		 	 	 			  		 			 	 	 		 		 	
        :return: The selected action  		  	   		 	 	 			  		 			 	 	 		 		 	
        :rtype: int  		  	   		 	 	 			  		 			 	 	 		 		 	
        """  		  	   		 	 	 			  		 			 	 	 		 		 	
        self.s = s  		  	   		 	 	 			  		 			 	 	 		 		 	
        action = self._get_next(s)  		  	   		 	 	 			  		 			 	 	 		 		 	
        logger.debug(f"s = {s}, a = {action}")
        self.a = action  		  	   		 	 	 			  		 			 	 	 		 		 	
        return action  		  	   		 	 	 			  		 			 	 	 		 		 	
  		  	   		 	 	 			  		 			 	 	 		 		 	
    def query(self, s_prime, r):  		  	   		 	 	 			  		 			 	 	 		 		 	
        """  		  	   		 	 	 			  		 			 	 	 		 		 	
        Update the Q table and return an action  		  	   		 	 	 			  		 			 	 	 		 		 	
  		  	   		 	 	 			  		 			 	 	 		 		 	
        :param s_prime: The new state  		  	   		 	 	 			  		 			 	 	 		 		 	
        :type s_prime: int  		  	   		 	 	 			  		 			 	 	 		 		 	
        :param r: The immediate reward  		  	   		 	 	 			  		 			 	 	 		 		 	
        :type r: float  		  	   		 	 	 			  		 			 	 	 		 		 	
        :return: The selected action  		  	   		 	 	 			  		 			 	 	 		 		 	
        :rtype: int  		  	   		 	 	 			  		 			 	 	 		 		 	
        """  		  	   		 	 	 		
        current_estimate = self.q[self.s, self.a]
        # this is the max future reward
        a_prime = np.argmax(self.q[s_prime, :])
        new_estimate = r + self.gamma * self.q[s_prime, a_prime]		 			 	 	 		 		 	
	  		 			 	 	 		 		 	  		  	   		 	 	
        self.q[self.s, self.a] = (1-self.alpha) * current_estimate + (self.alpha * new_estimate) 			  
        action = self._get_next(s_prime)

        logger.debug(f"s = {s_prime}, a = {action}, r={r}, current={current_estimate}, new={new_estimate}")  		  	   		 	 	 			  		 			 	 	 		 		 	
        self.s = s_prime
        self.a = action
        self.rar *= self.radr
        return action  		  	   		 	 	 			  		 			 	 	 		 		 	
    
    def author(self):  		  	   		 	 	 			  		 			 	 	 		 		 	
        """  		  	   		 	 	 			  		 			 	 	 		 		 	
        :return: The GT username of the student  		  	   		 	 	 			  		 			 	 	 		 		 	
        :rtype: str  		  	   		 	 	 			  		 			 	 	 		 		 	
        """  		  	   		 	 	 			  		 			 	 	 		 		 	
        return "hqian36"  		 	 	 			  		 			 	 	 		 		 	
  		  	   		 	 	 			  		 			 	 	 		 		 	
  		  	   		 	 	 			  		 			 	 	 		 		 	
    def study_group(self):
        """
        Returns
            A comma separated string of GT_Name of each member of your study group
            Example: "gburdell3, jdoe77, tbalch7" or "gburdell3" if a single individual working alone

        Return type
            str
        """
        return "hqian36"


    def gtid(self):  		  	   		 	 	 			  		 			 	 	 		 		 	
        """  		  	   		 	 	 			  		 			 	 	 		 		 	
        :return: The GT ID of the student  		  	   		 	 	 			  		 			 	 	 		 		 	
        :rtype: int  		  	   		 	 	 			  		 			 	 	 		 		 	
        """  		  	   		 	 	 			  		 			 	 	 		 		 	
        return 903538227
  		  	   		 	 	 			  		 			 	 	 		 		 	
  		  	   		 	 	 			  		 			 	 	 		 		 	
if __name__ == "__main__":  		  	   		 	 	 			  		 			 	 	 		 		 	
    print("Remember Q from Star Trek? Well, this isn't him")  		  	   		 	 	 			  		 			 	 	 		 		 	
